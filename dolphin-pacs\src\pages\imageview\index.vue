<template>
  <div class="fabric-video-viewer">
    <div class="controls">
      <button
        @click="connect"
        :disabled="isConnected"
        class="btn btn-connect"
      >
        {{ isConnected ? '已连接' : '连接' }}
      </button>
      <button
        @click="disconnect"
        :disabled="!isConnected"
        class="btn btn-disconnect"
      >
        断开连接
      </button>
    </div>
    
    <div class="canvas-container">
      <span class="status" :class="{ connected: isConnected, disconnected: !isConnected }">
        {{ connectionStatus }}
      </span>
      <canvas
        ref="canvasRef"
      ></canvas>
      <div class="info">
        <p>帧率: {{ frameRate }} FPS　总帧数: {{ frameCount }}</p>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
import { ref, onMounted, onUnmounted, nextTick } from 'vue'
import * as fabric from 'fabric'
// 接口定义
interface Props {
  websocketUrl?: string
  canvasWidth?: number
  canvasHeight?: number
  imageType?: string
}

interface ExposedMethods {
  connect: () => void
  disconnect: () => void
  isConnected: Readonly<Ref<boolean>>
}

// Props 定义
const props = withDefaults(defineProps<Props>(), {
  websocketUrl: 'ws://*************:14580/ws/video',
  canvasWidth: 800,
  canvasHeight: 600,
  imageType: 'image/jpeg'
})

// 响应式数据
const canvasRef = ref<HTMLCanvasElement | null>(null)
const socket = ref<WebSocket | null>(null)
const fabricCanvas = ref<fabric.Canvas | null>(null)
const isConnected = ref<boolean>(false)
const connectionStatus = ref<string>('未连接')
const frameRate = ref<number>(0)
const frameCount = ref<number>(0)
const currentImageObject = ref<fabric.Image | null>(null)

// 帧率计算相关
let lastFrameTime: number = 0
let frameRateCounter: number = 0
let frameRateInterval: NodeJS.Timeout | null = null

// 连接状态枚举
enum ConnectionStatus {
  DISCONNECTED = '未连接',
  CONNECTING = '连接中...',
  CONNECTED = '已连接',
  CLOSED = '连接已关闭',
  ERROR = '连接错误'
}

// 初始化 Fabric.js 画布
const initFabricCanvas = async (): Promise<void> => {
  await nextTick()

  if (!canvasRef.value) {
    console.error('Canvas element not found')
    return
  }

  // 获取容器尺寸
  const container = canvasRef.value.parentElement
  if (!container) {
    console.error('Canvas container not found')
    return
  }

  const containerWidth = container.clientWidth
  const containerHeight = container.clientHeight

  fabricCanvas.value = new fabric.Canvas(canvasRef.value, {
    width: containerWidth,
    height: containerHeight,
    backgroundColor: '#000000',
    selection: false, // 禁用选择
    preserveObjectStacking: true
  })

  console.log('✅ Fabric.js 画布初始化成功', `尺寸: ${containerWidth}x${containerHeight}`)
}

// 连接 WebSocket
const connect = (): void => {
  if (socket.value && socket.value.readyState === WebSocket.OPEN) {
    console.log('✅ 已经连接')
    return
  }

  connectionStatus.value = ConnectionStatus.CONNECTING
  
  socket.value = new WebSocket(props.websocketUrl)
  socket.value.binaryType = 'arraybuffer'

  socket.value.onopen = (): void => {
    console.log('✅ WebSocket 连接成功')
    isConnected.value = true
    connectionStatus.value = ConnectionStatus.CONNECTED
    startFrameRateCounter()
  }

  socket.value.onmessage = async (event: MessageEvent): Promise<void> => {
    if (event.data instanceof ArrayBuffer) {
      await displayFrame(event.data)
      updateFrameRate()
      frameCount.value++
    }
  }

  socket.value.onclose = (event: CloseEvent): void => {
    console.log('🔌 连接已关闭:', event.code, event.reason)
    isConnected.value = false
    connectionStatus.value = ConnectionStatus.CLOSED
    socket.value = null
    stopFrameRateCounter()
  }

  socket.value.onerror = (error: Event): void => {
    console.error('❌ WebSocket 错误:', error)
    isConnected.value = false
    connectionStatus.value = ConnectionStatus.ERROR
    stopFrameRateCounter()
  }
}

// 断开连接
const disconnect = (): void => {
  if (socket.value) {
    socket.value.close()
  }
  stopFrameRateCounter()
}

// 显示帧到 Fabric.js 画布
const displayFrame = async (arrayBuffer: ArrayBuffer): Promise<void> => {
  try {
    // 创建 Blob 和 ImageBitmap
    const blob: Blob = new Blob([arrayBuffer], { type: props.imageType })
    const imageBitmap: ImageBitmap = await createImageBitmap(blob)
    
    // 创建临时 canvas 来获取图像数据
    const tempCanvas: HTMLCanvasElement = document.createElement('canvas')
    const tempCtx: CanvasRenderingContext2D | null = tempCanvas.getContext('2d')
    
    if (!tempCtx) {
      throw new Error('无法获取 canvas context')
    }
    
    tempCanvas.width = imageBitmap.width
    tempCanvas.height = imageBitmap.height
    
    // 将 ImageBitmap 绘制到临时 canvas
    tempCtx.drawImage(imageBitmap, 0, 0)
    
    // 创建 Fabric.js Image 对象
    const imgElement: HTMLImageElement = new Image()
    
    imgElement.onload = (): void => {
      if (!fabricCanvas.value) return

      const canvasWidth = fabricCanvas.value.width || props.canvasWidth
      const canvasHeight = fabricCanvas.value.height || props.canvasHeight

      const fabricImage: fabric.Image = new fabric.Image(imgElement, {
        left: 0,
        top: 0,
        scaleX: canvasWidth / imgElement.width,
        scaleY: canvasHeight / imgElement.height,
        selectable: false,
        evented: false
      })

      // 移除之前的图像
      if (currentImageObject.value) {
        fabricCanvas.value.remove(currentImageObject.value)
      }

      // 添加新图像
      fabricCanvas.value.add(fabricImage)
      currentImageObject.value = fabricImage
      
      // 渲染画布
      fabricCanvas.value.renderAll()
    }
    
    imgElement.onerror = (error: string | Event): void => {
      console.error('图像加载失败:', error)
    }
    
    // 设置图像源
    imgElement.src = tempCanvas.toDataURL()
    
    // 释放资源
    imageBitmap.close()
    
  } catch (error: unknown) {
    console.error('显示帧失败:', error)
  }
}

// 启动帧率计数器
const startFrameRateCounter = (): void => {
  frameRateInterval = setInterval((): void => {
    frameRate.value = frameRateCounter
    frameRateCounter = 0
  }, 1000)
}

// 停止帧率计数器
const stopFrameRateCounter = (): void => {
  if (frameRateInterval) {
    clearInterval(frameRateInterval)
    frameRateInterval = null
  }
  frameRate.value = 0
  frameRateCounter = 0
}

// 更新帧率
const updateFrameRate = (): void => {
  frameRateCounter++
}

// 重置统计信息
const resetStats = (): void => {
  frameCount.value = 0
  frameRate.value = 0
  frameRateCounter = 0
}

// 调整画布大小
const resizeCanvas = (): void => {
  if (!fabricCanvas.value || !canvasRef.value) return

  const container = canvasRef.value.parentElement
  if (!container) return

  const containerWidth = container.clientWidth
  const containerHeight = container.clientHeight

  fabricCanvas.value.setDimensions({
    width: containerWidth,
    height: containerHeight
  })

  // 如果有当前图像，重新调整其大小
  if (currentImageObject.value) {
    const imgElement = currentImageObject.value.getElement() as HTMLImageElement
    if (imgElement) {
      currentImageObject.value.set({
        scaleX: containerWidth / imgElement.width,
        scaleY: containerHeight / imgElement.height
      })
      fabricCanvas.value.renderAll()
    }
  }
}

// 组件挂载
onMounted((): void => {
  initFabricCanvas()
  window.addEventListener('resize', resizeCanvas)
})

// 组件卸载
onUnmounted((): void => {
  disconnect()
  window.removeEventListener('resize', resizeCanvas)
  if (fabricCanvas.value) {
    fabricCanvas.value.dispose()
  }
})

// 暴露方法给父组件
defineExpose<ExposedMethods>({
  connect,
  disconnect,
  isConnected: readonly(isConnected)
})
</script>

<style scoped>
.fabric-video-viewer {
  display: flex;
  flex-direction: column;
  gap: 16px;
  padding: 20px;
  background: #f5f5f5;
  border-radius: 8px;
  height: 100vh;
}

.canvas-container {
  flex: 1;
  position: relative;
  display: flex;
  justify-content: center;
  align-items: center;
  background: #000;
  border-radius: 4px;
  padding: 0;
  min-height: 500px;
}

.controls {
  display: flex;
  align-items: center;
  gap: 12px;
}

.btn {
  padding: 8px 16px;
  border: none;
  border-radius: 4px;
  cursor: pointer;
  font-size: 14px;
  transition: all 0.2s;
}

.btn:disabled {
  opacity: 0.6;
  cursor: not-allowed;
}

.btn-connect {
  background: #4CAF50;
  color: white;
}

.btn-connect:hover:not(:disabled) {
  background: #45a049;
}

.btn-disconnect {
  background: #f44336;
  color: white;
}

.btn-disconnect:hover:not(:disabled) {
  background: #da190b;
}

.status {
  position: absolute;
  top: 10px;
  right: 10px;
  padding: 8px 12px;
  border-radius: 4px;
  font-size: 12px;
  font-weight: bold;
  z-index: 10;
  background: transparent;
}

.status.connected {
  color: #4CAF50;
}

.status.disconnected {
  color: #f44336;
}



.canvas-container canvas {
  border: none;
  border-radius: 4px;
  width: 100% !important;
  height: 100% !important;
}

.info {
  position: absolute;
  top: 50px;
  right: 10px;
  display: flex;
  gap: 20px;
  font-size: 14px;
  color: #fff;
  background: transparent;
  padding: 8px 12px;
  border-radius: 4px;
  z-index: 10;
}

.info p {
  margin: 0;
}
</style>